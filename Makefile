# MealPal Makefile

# Variables
APP_NAME := mealpal
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Go variables
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod

# Build flags
LDFLAGS := -ldflags "-X main.version=$(VERSION) -X main.buildTime=$(BUILD_TIME) -X main.gitCommit=$(GIT_COMMIT)"

# Default target
.PHONY: all
all: clean deps test build

# Help target
.PHONY: help
help: ## Show this help message
	@echo "MealPal - AI Food Logging Assistant"
	@echo ""
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development targets
.PHONY: run
run: ## Run the application in development mode
	@echo "Starting MealPal in development mode..."
	$(GOCMD) run cmd/main.go

.PHONY: dev
dev: deps run ## Install dependencies and run in development mode

# Build targets
.PHONY: build
build: ## Build the application for current platform
	@echo "Building $(APP_NAME) for current platform..."
	$(GOBUILD) $(LDFLAGS) -o bin/$(APP_NAME) cmd/main.go

.PHONY: build-local
build-local: clean deps build ## Clean, install deps, and build for current platform

# Cross-platform build using goreleaser
.PHONY: build-all
build-all: ## Build for all platforms using goreleaser (requires goreleaser)
	@echo "Building $(APP_NAME) for all platforms using goreleaser..."
	@if ! command -v goreleaser >/dev/null 2>&1; then \
		echo "Error: goreleaser is not installed. Install it with:"; \
		echo "  brew install goreleaser/tap/goreleaser  # macOS"; \
		echo "  or visit: https://goreleaser.com/install/"; \
		exit 1; \
	fi
	goreleaser build --snapshot --clean

.PHONY: build-linux
build-linux: ## Build for Linux x64 using goreleaser
	@echo "Building $(APP_NAME) for Linux x64..."
	@if ! command -v goreleaser >/dev/null 2>&1; then \
		echo "Error: goreleaser is not installed. Install it with:"; \
		echo "  brew install goreleaser/tap/goreleaser  # macOS"; \
		echo "  or visit: https://goreleaser.com/install/"; \
		exit 1; \
	fi
	goreleaser build --snapshot --clean --single-target --id linux

# Release targets
.PHONY: release
release: ## Create a release using goreleaser (requires git tag)
	@echo "Creating release..."
	@if ! command -v goreleaser >/dev/null 2>&1; then \
		echo "Error: goreleaser is not installed"; \
		exit 1; \
	fi
	goreleaser release

.PHONY: release-snapshot
release-snapshot: ## Create a snapshot release using goreleaser
	@echo "Creating snapshot release..."
	@if ! command -v goreleaser >/dev/null 2>&1; then \
		echo "Error: goreleaser is not installed"; \
		exit 1; \
	fi
	goreleaser release --snapshot --clean

# Dependency management
.PHONY: deps
deps: ## Download and install dependencies
	@echo "Installing dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

.PHONY: deps-update
deps-update: ## Update all dependencies
	@echo "Updating dependencies..."
	$(GOGET) -u ./...
	$(GOMOD) tidy

# Testing
.PHONY: test
test: ## Run tests
	@echo "Running tests..."
	$(GOTEST) -v ./...

.PHONY: test-coverage
test-coverage: ## Run tests with coverage
	@echo "Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Linting and formatting
.PHONY: fmt
fmt: ## Format Go code
	@echo "Formatting code..."
	$(GOCMD) fmt ./...

.PHONY: vet
vet: ## Run go vet
	@echo "Running go vet..."
	$(GOCMD) vet ./...

.PHONY: lint
lint: fmt vet ## Run formatting and vetting

# Cleaning
.PHONY: clean
clean: ## Clean build artifacts
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf bin/
	rm -rf dist/
	rm -f coverage.out coverage.html

# Docker targets (optional)
.PHONY: docker-build
docker-build: ## Build Docker image
	@echo "Building Docker image..."
	docker build -t $(APP_NAME):$(VERSION) .

# Installation
.PHONY: install
install: build ## Install the binary to $GOPATH/bin
	@echo "Installing $(APP_NAME)..."
	cp bin/$(APP_NAME) $(GOPATH)/bin/

# Quick development workflow
.PHONY: quick
quick: clean deps lint test build ## Quick development workflow: clean, deps, lint, test, build

# Server deployment helpers
.PHONY: deploy-build
deploy-build: ## Build for Linux server deployment
	@echo "Building for Linux server deployment..."
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o bin/$(APP_NAME)-linux-amd64 cmd/main.go
	@echo "Binary created: bin/$(APP_NAME)-linux-amd64"

# Environment setup
.PHONY: setup
setup: ## Set up development environment
	@echo "Setting up development environment..."
	@if [ ! -f .env ]; then \
		echo "Creating .env file from .env.example..."; \
		cp .env.example .env; \
		echo "Please edit .env file with your API keys"; \
	fi
	$(MAKE) deps
	@echo "Setup complete! Edit .env file with your API keys, then run 'make run'"

# Check if goreleaser is installed
.PHONY: check-goreleaser
check-goreleaser: ## Check if goreleaser is installed
	@if command -v goreleaser >/dev/null 2>&1; then \
		echo "✅ goreleaser is installed: $$(goreleaser --version)"; \
	else \
		echo "❌ goreleaser is not installed"; \
		echo "Install it with:"; \
		echo "  brew install goreleaser/tap/goreleaser  # macOS"; \
		echo "  or visit: https://goreleaser.com/install/"; \
	fi
