package main

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/config"
	"github.com/stefanoschrs/mealpal/internal/handlers"
	"github.com/stefanoschrs/mealpal/internal/middleware"
	"github.com/stefanoschrs/mealpal/internal/services"
)

func main() {
	// Load configuration
	cfg := config.Load()

	// Set Gin mode based on environment
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize services
	authService := services.NewAuthService(cfg)
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	// Initialize session store
	store := sessions.NewCookieStore([]byte(cfg.SessionSecret))
	store.Options = &sessions.Options{
		Path:     "/",
		MaxAge:   86400 * 7, // 7 days
		HttpOnly: true,
		Secure:   cfg.Environment == "production", // Only use secure cookies in production
		SameSite: http.SameSiteLaxMode,
	}

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService, store)
	appHandler := handlers.NewAppHandler(foodLogService, authService, store)

	// Initialize Gin router
	router := gin.Default()

	// Load HTML templates
	router.LoadHTMLGlob("templates/*")

	// Serve static files
	router.Static("/static", "./static")

	// Public routes
	router.GET("/", appHandler.Home)
	router.GET("/error", appHandler.Error)

	// Auth routes
	auth := router.Group("/auth")
	{
		auth.GET("/google/login", authHandler.Login)
		auth.GET("/google/callback", authHandler.Callback)
		auth.GET("/logout", authHandler.Logout)
	}

	// Protected routes
	protected := router.Group("/")
	protected.Use(middleware.AuthRequired(store))
	{
		protected.GET("/dashboard", appHandler.Dashboard)
		protected.POST("/submit-food", appHandler.SubmitFood)
		protected.GET("/api/spreadsheet-info", appHandler.GetSpreadsheetInfo)
	}

	// Start server
	log.Printf("Starting MealPal server on port %s", cfg.Port)
	log.Printf("Environment: %s", cfg.Environment)

	if err := router.Run(":" + cfg.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
