# 🍽️ MealPal - AI Food Logging Assistant

MealPal is a Progressive Web App (PWA) that helps you log your daily food intake using AI-powered analysis and automatic Google Sheets integration.

## Features

- 🤖 **AI-Powered Food Analysis**: Uses Google Gemini to parse and analyze your food descriptions
- 📊 **Google Sheets Integration**: Automatically logs parsed food data to your Google Drive spreadsheets
- 🔐 **Google OAuth Authentication**: Secure login with your Google account
- 📱 **Progressive Web App**: Install on your phone for quick access
- 🎨 **Modern UI**: Clean, responsive design with toast notifications
- ⚡ **Real-time Processing**: Instant food logging with loading states

## Prerequisites

Before running MealPal, you'll need to set up the following:

### 1. Google Cloud Console Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the following APIs:
   - Google+ API (for OAuth)
   - Google Sheets API
   - Google Drive API

4. Create OAuth 2.0 credentials:
   - Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
   - Application type: "Web application"
   - Authorized redirect URIs: `http://localhost:8080/auth/google/callback`
   - Note down your Client ID and Client Secret

### 2. Google Gemini API Setup

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key for Gemini
3. Note down your API key

## Installation & Setup

1. **Clone and navigate to the project**:
   ```bash
   cd /path/to/mealpal
   ```

2. **Install dependencies**:
   ```bash
   go mod tidy
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and fill in your actual values:
   ```env
   GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   GEMINI_API_KEY=your-gemini-api-key
   SESSION_SECRET=a-long-random-string-for-sessions
   ```

4. **Create PWA icons** (optional but recommended):
   - Add icon files to `static/icons/` directory
   - Use the sizes specified in `static/icons/README.md`
   - You can use online tools like [PWA Builder](https://www.pwabuilder.com/imageGenerator)

5. **Run the application**:
   ```bash
   go run cmd/main.go
   ```

6. **Access the application**:
   - Open your browser and go to `http://localhost:8080`
   - Click "Continue with Google" to authenticate
   - Start logging your food!

## Usage

1. **Login**: Use your Google account to sign in
2. **Log Food**: Describe your meal in natural language, for example:
   - "I had a chicken caesar salad with grilled chicken breast, romaine lettuce, parmesan cheese, and caesar dressing for lunch at 1:30 PM"
   - "Breakfast: 2 scrambled eggs, 2 slices of whole wheat toast with butter, and a cup of coffee"
   - "Snack: 1 medium apple and 10 almonds"

3. **AI Processing**: MealPal will:
   - Parse your description using Google Gemini
   - Extract individual food items, quantities, and estimated calories
   - Format the data as CSV

4. **Google Sheets**: The parsed data is automatically saved to a new spreadsheet in your Google Drive

## Project Structure

```
mealpal/
├── cmd/
│   └── main.go                 # Application entry point
├── internal/
│   ├── config/
│   │   └── config.go          # Configuration management
│   ├── handlers/
│   │   ├── auth.go            # Authentication handlers
│   │   └── app.go             # Application handlers
│   ├── middleware/
│   │   └── auth.go            # Authentication middleware
│   ├── models/
│   │   └── user.go            # Data models
│   └── services/
│       ├── auth.go            # Google OAuth service
│       ├── gemini.go          # Google Gemini API service
│       ├── sheets.go          # Google Sheets API service
│       └── foodlog.go         # Food logging coordination
├── templates/
│   ├── base.html              # Base HTML template
│   ├── home.html              # Login page
│   ├── dashboard.html         # Main app interface
│   └── error.html             # Error page
├── static/
│   ├── css/
│   │   └── style.css          # Application styles
│   ├── js/
│   │   ├── app.js             # Frontend JavaScript
│   │   └── sw.js              # Service Worker for PWA
│   ├── icons/                 # PWA icons
│   └── manifest.json          # PWA manifest
├── .env.example               # Environment variables template
├── go.mod                     # Go module definition
└── README.md                  # This file
```

## Development

### Running in Development Mode

```bash
# Set environment to development in .env
ENVIRONMENT=development

# Run with auto-reload (install air first: go install github.com/cosmtrek/air@latest)
air

# Or run normally
go run cmd/main.go
```

### Building for Production

```bash
# Build binary
go build -o mealpal cmd/main.go

# Set production environment variables
export ENVIRONMENT=production
export GOOGLE_REDIRECT_URL=https://yourdomain.com/auth/google/callback

# Run
./mealpal
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

If you encounter any issues or have questions, please create an issue in the GitHub repository.
