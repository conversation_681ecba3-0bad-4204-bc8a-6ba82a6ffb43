# GoReleaser configuration for MealPal
version: 2

# Project information
project_name: mealpal

# Build configuration
builds:
  - id: linux
    main: ./cmd/main.go
    binary: mealpal
    goos:
      - linux
    goarch:
      - amd64
    env:
      - CGO_ENABLED=0
    ldflags:
      - -s -w
      - -X main.version={{.Version}}
      - -X main.buildTime={{.Date}}
      - -X main.gitCommit={{.ShortCommit}}
    tags:
      - netgo
      - osusergo
    
  - id: darwin
    main: ./cmd/main.go
    binary: mealpal
    goos:
      - darwin
    goarch:
      - amd64
      - arm64
    env:
      - CGO_ENABLED=0
    ldflags:
      - -s -w
      - -X main.version={{.Version}}
      - -X main.buildTime={{.Date}}
      - -X main.gitCommit={{.ShortCommit}}
    tags:
      - netgo
      - osusergo

  - id: windows
    main: ./cmd/main.go
    binary: mealpal
    goos:
      - windows
    goarch:
      - amd64
    env:
      - CGO_ENABLED=0
    ldflags:
      - -s -w
      - -X main.version={{.Version}}
      - -X main.buildTime={{.Date}}
      - -X main.gitCommit={{.ShortCommit}}
    tags:
      - netgo
      - osusergo

# Archive configuration
archives:
  - id: default
    format: tar.gz
    name_template: "{{ .ProjectName }}_{{ .Version }}_{{ .Os }}_{{ .Arch }}"
    format_overrides:
      - goos: windows
        format: zip
    files:
      - README.md
      - LICENSE*
      - .env.example
      - templates/**/*
      - static/**/*

# Checksum configuration
checksum:
  name_template: "{{ .ProjectName }}_{{ .Version }}_checksums.txt"
  algorithm: sha256

# Snapshot configuration
snapshot:
  name_template: "{{ .Version }}-SNAPSHOT-{{ .ShortCommit }}"

# Changelog configuration
changelog:
  sort: asc
  use: github
  filters:
    exclude:
      - "^docs:"
      - "^test:"
      - "^ci:"
      - "^chore:"
      - "merge conflict"
      - Merge pull request
      - Merge remote-tracking branch
      - Merge branch
  groups:
    - title: "New Features"
      regexp: "^.*feat[(\\w)]*:+.*$"
      order: 0
    - title: "Bug Fixes"
      regexp: "^.*fix[(\\w)]*:+.*$"
      order: 10
    - title: "Documentation"
      regexp: "^.*docs[(\\w)]*:+.*$"
      order: 20
    - title: "Other Changes"
      order: 999

# Release configuration
release:
  github:
    owner: stefanoschrs
    name: mealpal
  draft: false
  prerelease: auto
  mode: replace
  header: |
    ## MealPal {{ .Tag }}
    
    Welcome to this new release of MealPal! 🍽️
    
    ### What's New
  footer: |
    ## Installation
    
    ### Quick Start
    1. Download the appropriate binary for your platform
    2. Extract the archive
    3. Copy `.env.example` to `.env` and configure your API keys
    4. Run `./mealpal`
    
    ### Requirements
    - Google OAuth 2.0 credentials
    - Google Gemini API key
    
    **Full Changelog**: https://github.com/stefanoschrs/mealpal/compare/{{ .PreviousTag }}...{{ .Tag }}

# Disable features we don't need
dockers:
  - skip_push: true
    
brews:
  - skip_upload: true

nfpms:
  - skip_upload: true

publishers:
  - skip_upload: true
