// MealPal Service Worker - Basic PWA functionality

const CACHE_NAME = 'mealpal-v1';

// Install event - just activate immediately
self.addEventListener('install', function(event) {
    console.log('Service Worker installed');
    self.skipWaiting();
});

// Activate event - take control immediately
self.addEventListener('activate', function(event) {
    console.log('Service Worker activated');
    event.waitUntil(self.clients.claim());
});

// Fetch event - always fetch from network (no offline caching)
self.addEventListener('fetch', function(event) {
    // Just pass through to network, no caching
    event.respondWith(fetch(event.request));
});
