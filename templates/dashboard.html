{{define "content"}}
<div class="app-container">
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-left">
            <h1 class="logo">🍽️ MealPal</h1>
        </div>
        <div class="nav-right">
            <div class="user-menu">
                <img src="{{.user.Picture}}" alt="{{.user.Name}}" class="user-avatar" id="userAvatar">
                <div class="dropdown-menu" id="dropdownMenu">
                    <div class="dropdown-header">
                        <img src="{{.user.Picture}}" alt="{{.user.Name}}" class="dropdown-avatar">
                        <div class="dropdown-user-info">
                            <div class="dropdown-name">{{.user.Name}}</div>
                            <div class="dropdown-email">{{.user.Email}}</div>
                        </div>
                    </div>
                    <div class="dropdown-divider"></div>
                    <a href="/auth/logout" class="dropdown-item">
                        <span class="dropdown-icon">🚪</span>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="content-container">
            <div class="welcome-section">
                <h2>Welcome back, {{.user.Name}}! 👋</h2>
                <p>What did you eat today? Describe your meal and I'll log it for you.</p>
            </div>

            <!-- Food Input Form -->
            <div class="food-form-container">
                <form id="foodForm" class="food-form">
                    <div class="form-group">
                        <label for="foodText">Describe your meal:</label>
                        <textarea 
                            id="foodText" 
                            name="food_text" 
                            placeholder="e.g., I had a chicken caesar salad with grilled chicken breast, romaine lettuce, parmesan cheese, and caesar dressing for lunch at 1:30 PM"
                            rows="4"
                            required
                        ></textarea>
                    </div>
                    <button type="submit" class="submit-btn" id="submitBtn">
                        <span class="btn-text">Log Food</span>
                        <span class="btn-spinner" style="display: none;">
                            <div class="spinner"></div>
                        </span>
                    </button>
                </form>
            </div>

            <!-- Tips Section -->
            <div class="tips-section">
                <h3>💡 Tips for better logging:</h3>
                <ul class="tips-list">
                    <li>Include specific quantities (e.g., "1 cup of rice", "2 slices of bread")</li>
                    <li>Mention cooking methods (e.g., "grilled", "fried", "steamed")</li>
                    <li>Add timing if different from now (e.g., "for breakfast at 8 AM")</li>
                    <li>Include brands or restaurant names when relevant</li>
                </ul>
            </div>
        </div>
    </main>
</div>
{{end}}
