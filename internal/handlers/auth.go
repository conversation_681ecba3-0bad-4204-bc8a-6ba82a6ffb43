package handlers

import (
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/middleware"
	"github.com/stefanoschrs/mealpal/internal/models"
	"github.com/stefanoschrs/mealpal/internal/services"
	"golang.org/x/oauth2"
)

type AuthHandler struct {
	authService *services.AuthService
	store       sessions.Store
}

func NewAuthHandler(authService *services.AuthService, store sessions.Store) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		store:       store,
	}
}

func (h *AuthHandler) Login(c *gin.Context) {
	// Generate a random state token
	state := generateStateToken()
	
	// Store state in session for verification
	session, _ := h.store.Get(c.Request, middleware.SessionName)
	session.Values["oauth_state"] = state
	session.Save(c.Request, c.<PERSON>)

	// Redirect to Google OAuth
	authURL := h.authService.GetAuthURL(state)
	c.Redirect(http.StatusFound, authURL)
}

func (h *AuthHandler) Callback(c *gin.Context) {
	// Verify state token
	session, err := h.store.Get(c.Request, middleware.SessionName)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Session error",
		})
		return
	}

	storedState, ok := session.Values["oauth_state"].(string)
	if !ok || storedState != c.Query("state") {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid state token",
		})
		return
	}

	// Exchange code for token
	code := c.Query("code")
	if code == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "No authorization code received",
		})
		return
	}

	token, err := h.authService.ExchangeCode(c.Request.Context(), code)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to exchange authorization code",
		})
		return
	}

	// Get user info
	user, err := h.authService.GetUserInfo(c.Request.Context(), token)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to get user information",
		})
		return
	}

	// Store user and token in session
	sessionData := models.SessionData{
		User:       user,
		OAuthToken: token,
		IsLoggedIn: true,
	}

	sessionDataJSON, err := json.Marshal(sessionData)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to save session",
		})
		return
	}

	session.Values["session_data"] = string(sessionDataJSON)
	session.Save(c.Request, c.Writer)

	// Redirect to dashboard
	c.Redirect(http.StatusFound, "/dashboard")
}

func (h *AuthHandler) Logout(c *gin.Context) {
	session, _ := h.store.Get(c.Request, middleware.SessionName)
	
	// Clear session
	session.Values = make(map[interface{}]interface{})
	session.Options.MaxAge = -1
	session.Save(c.Request, c.Writer)

	c.Redirect(http.StatusFound, "/")
}

func generateStateToken() string {
	b := make([]byte, 32)
	rand.Read(b)
	return base64.URLEncoding.EncodeToString(b)
}
