package services

import (
	"context"
	"fmt"
	"strings"

	"github.com/stefanoschrs/mealpal/internal/models"
	"golang.org/x/oauth2"
	"google.golang.org/api/option"
	"google.golang.org/api/sheets/v4"
)

type SheetsService struct {
	oauthConfig *oauth2.Config
}

func NewSheetsService(oauthConfig *oauth2.Config) *SheetsService {
	return &SheetsService{
		oauthConfig: oauthConfig,
	}
}

func (s *SheetsService) AddFoodEntry(ctx context.Context, user *models.User, token *oauth2.Token, csvData string) error {
	client := s.oauthConfig.Client(ctx, token)
	
	service, err := sheets.NewService(ctx, option.WithHTTPClient(client))
	if err != nil {
		return fmt.Errorf("failed to create sheets service: %w", err)
	}

	// Get or create the MealPal spreadsheet
	spreadsheetID, err := s.getOrCreateSpreadsheet(service, user)
	if err != nil {
		return fmt.Errorf("failed to get/create spreadsheet: %w", err)
	}

	// Parse CSV data and add rows
	rows := s.parseCSVToRows(csvData)
	if len(rows) == 0 {
		return fmt.Errorf("no valid data to add")
	}

	// Add the rows to the spreadsheet
	valueRange := &sheets.ValueRange{
		Values: rows,
	}

	_, err = service.Spreadsheets.Values.Append(
		spreadsheetID,
		"Sheet1!A:F", // Assuming 6 columns: Date, Time, Food Item, Quantity, Calories, Notes
		valueRange,
	).ValueInputOption("USER_ENTERED").Do()

	if err != nil {
		return fmt.Errorf("failed to append data to spreadsheet: %w", err)
	}

	return nil
}

func (s *SheetsService) getOrCreateSpreadsheet(service *sheets.Service, user *models.User) (string, error) {
	// For now, we'll create a new spreadsheet each time
	// In a production app, you might want to store the spreadsheet ID per user
	
	spreadsheet := &sheets.Spreadsheet{
		Properties: &sheets.SpreadsheetProperties{
			Title: "MealPal Food Log",
		},
		Sheets: []*sheets.Sheet{
			{
				Properties: &sheets.SheetProperties{
					Title: "Sheet1",
				},
			},
		},
	}

	resp, err := service.Spreadsheets.Create(spreadsheet).Do()
	if err != nil {
		return "", err
	}

	// Add headers to the new spreadsheet
	headers := [][]interface{}{
		{"Date", "Time", "Food Item", "Quantity", "Estimated Calories", "Notes"},
	}

	headerRange := &sheets.ValueRange{
		Values: headers,
	}

	_, err = service.Spreadsheets.Values.Update(
		resp.SpreadsheetId,
		"Sheet1!A1:F1",
		headerRange,
	).ValueInputOption("USER_ENTERED").Do()

	if err != nil {
		return "", fmt.Errorf("failed to add headers: %w", err)
	}

	return resp.SpreadsheetId, nil
}

func (s *SheetsService) parseCSVToRows(csvData string) [][]interface{} {
	lines := strings.Split(strings.TrimSpace(csvData), "\n")
	var rows [][]interface{}

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		// Simple CSV parsing (for production, consider using encoding/csv)
		fields := strings.Split(line, ",")
		row := make([]interface{}, len(fields))
		for i, field := range fields {
			// Remove quotes if present
			field = strings.Trim(strings.TrimSpace(field), "\"")
			row[i] = field
		}
		rows = append(rows, row)
	}

	return rows
}
