package services

import (
	"sync"

	"github.com/stefanoschrs/mealpal/internal/models"
)

// UserStore provides simple in-memory storage for user data
// In production, this should be replaced with a proper database
type UserStore struct {
	users map[string]*models.User
	mutex sync.RWMutex
}

func NewUserStore() *UserStore {
	return &UserStore{
		users: make(map[string]*models.User),
	}
}

func (s *UserStore) GetUser(userID string) (*models.User, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	user, exists := s.users[userID]
	if !exists {
		return nil, false
	}
	
	// Return a copy to avoid race conditions
	userCopy := *user
	return &userCopy, true
}

func (s *UserStore) SaveUser(user *models.User) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	// Store a copy to avoid race conditions
	userCopy := *user
	s.users[user.ID] = &userCopy
}

func (s *UserStore) UpdateSpreadsheetID(userID, spreadsheetID string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if user, exists := s.users[userID]; exists {
		user.SpreadsheetID = spreadsheetID
	}
}
