package services

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"

	"github.com/stefanoschrs/mealpal/internal/models"
)

// UserStore provides persistent file-based storage for user data
type UserStore struct {
	users    map[string]*models.User
	mutex    sync.RWMutex
	filePath string
}

func NewUserStore() *UserStore {
	// Create data directory if it doesn't exist
	dataDir := "data"
	os.MkdirAll(dataDir, 0755)

	filePath := filepath.Join(dataDir, "users.json")

	store := &UserStore{
		users:    make(map[string]*models.User),
		filePath: filePath,
	}

	// Load existing data
	store.loadFromFile()

	return store
}

func (s *UserStore) GetUser(userID string) (*models.User, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	user, exists := s.users[userID]
	if !exists {
		return nil, false
	}
	
	// Return a copy to avoid race conditions
	userCopy := *user
	return &userCopy, true
}

func (s *UserStore) SaveUser(user *models.User) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Store a copy to avoid race conditions
	userCopy := *user
	s.users[user.ID] = &userCopy

	// Persist to file
	s.saveToFileUnsafe()
}

func (s *UserStore) UpdateSpreadsheetID(userID, spreadsheetID string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if user, exists := s.users[userID]; exists {
		user.SpreadsheetID = spreadsheetID
		// Persist to file
		s.saveToFileUnsafe()
	}
}

// loadFromFile loads user data from the JSON file
func (s *UserStore) loadFromFile() {
	data, err := os.ReadFile(s.filePath)
	if err != nil {
		if os.IsNotExist(err) {
			// File doesn't exist yet, that's okay
			return
		}
		fmt.Printf("Warning: Failed to load user data: %v\n", err)
		return
	}

	var users map[string]*models.User
	if err := json.Unmarshal(data, &users); err != nil {
		fmt.Printf("Warning: Failed to parse user data: %v\n", err)
		return
	}

	s.users = users
	fmt.Printf("Loaded %d users from storage\n", len(users))
}

// saveToFileUnsafe saves user data to the JSON file (must be called with mutex locked)
func (s *UserStore) saveToFileUnsafe() {
	data, err := json.MarshalIndent(s.users, "", "  ")
	if err != nil {
		fmt.Printf("Warning: Failed to marshal user data: %v\n", err)
		return
	}

	if err := os.WriteFile(s.filePath, data, 0644); err != nil {
		fmt.Printf("Warning: Failed to save user data: %v\n", err)
		return
	}
}
