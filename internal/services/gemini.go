package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/stefanoschrs/mealpal/internal/config"
)

type GeminiService struct {
	apiKey string
	client *http.Client
}

type GeminiRequest struct {
	Contents []GeminiContent `json:"contents"`
}

type GeminiContent struct {
	Parts []GeminiPart `json:"parts"`
}

type GeminiPart struct {
	Text string `json:"text"`
}

type GeminiResponse struct {
	Candidates []GeminiCandidate `json:"candidates"`
}

type GeminiCandidate struct {
	Content GeminiContent `json:"content"`
}

func NewGeminiService(cfg *config.Config) *GeminiService {
	return &GeminiService{
		apiKey: cfg.GeminiAPIKey,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (s *GeminiService) ParseFoodText(foodText string) (string, error) {
	prompt := fmt.Sprintf(`
Please analyze the following food description and convert it to CSV format with the following columns:
Date, Time, Food Item, Quantity, Estimated Calories, Notes

Instructions:
- Use today's date (%s) if no date is specified
- Use current time if no time is specified
- Break down complex meals into individual food items (separate rows)
- Provide reasonable calorie estimates
- Include any relevant notes (preparation method, brand, etc.)
- Return ONLY the CSV data, no headers, no explanations
- Use comma separation and quote text fields that contain commas

Food description: %s
`, time.Now().Format("2006-01-02"), foodText)

	reqBody := GeminiRequest{
		Contents: []GeminiContent{
			{
				Parts: []GeminiPart{
					{Text: prompt},
				},
			},
		},
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	url := fmt.Sprintf("https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=%s", s.apiKey)
	
	resp, err := s.client.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to call Gemini API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Gemini API error (status %d): %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	var geminiResp GeminiResponse
	if err := json.Unmarshal(body, &geminiResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(geminiResp.Candidates) == 0 || len(geminiResp.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("no response from Gemini API")
	}

	csvData := strings.TrimSpace(geminiResp.Candidates[0].Content.Parts[0].Text)
	return csvData, nil
}
